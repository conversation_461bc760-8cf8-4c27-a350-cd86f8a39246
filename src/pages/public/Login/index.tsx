import { useState } from 'react'
import { Form, Input, But<PERSON>, Card, notification } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { routePaths } from '@/constants/routesElement'
import { useAuth } from '@/contexts/AuthContext'
import authApi from '@/services/internal/modules/auth'
import IconLogo from '@/assets/images/icon-logo.png'
import { NOT_FOUND, UNAUTHENTICATED } from '@/constants/status-response'
import { ILoginPayload } from '@/types/user'
import HeroBg from '@/assets/images/hero-bg.svg'
import { messages } from '@/constants/message'
import { LABELS, PLACEHOLDERS, RULE_VALIDATE } from './constant'
import { parseErrorDetails } from '@/utils/errorHandling'
import { trimObjectValues } from '@/utils/validationData'

const Login = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm<ILoginPayload>()
  const [loading, setLoading] = useState(false)
  const { handleLogin } = useAuth()

  const handleSubmit = async (values: ILoginPayload) => {
    try {
      setLoading(true)
      const trimmedValues = trimObjectValues(values)
      const response = await authApi.login(trimmedValues)
      handleLogin(response.accessToken, values.name)
      navigate(routePaths.dashboard)
    } catch (error) {
      handleLoginError(error)
    } finally {
      setLoading(false)
    }
  }

  const handleLoginError = (error: unknown) => {
    const { errorStatus, errorMessage } = parseErrorDetails(error)

    switch (errorStatus) {
      case NOT_FOUND:
        notification.error({
          placement: 'topRight',
          message: messages.error.incorrectUserNamePasword
        })
        break
      case UNAUTHENTICATED:
        notification.error({
          placement: 'topRight',
          message:
            errorMessage === 'Your account is not active'
              ? messages.error.deactiveAccount
              : messages.error.incorrectUserNamePasword
        })
        break
    }
  }

  return (
    <div
      className='flex flex-col min-h-screen items-center justify-center bg-gray-50 p-4'
      style={{
        backgroundImage: `url(${HeroBg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <div className='flex items-center mb-9'>
        <img src={IconLogo} alt='peer-conne logo' className='w-[183px]' />
        <p className='ml-3 text-[22px] font-semibold'>管理者サイト</p>
      </div>
      <Card className='w-full max-w-[534px] px-[40px] pt-[32px] pb-[24px] rounded-2xl [box-shadow:0px_4px_60px_0px_#********]'>
        <h1 className='mb-8 text-center text-[28px] leading-[22px] font-bold'>ログイン</h1>
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSubmit}
          autoComplete='off'
          requiredMark={false}
        >
          <Form.Item
            name='name'
            label={<span className='leading-8'>{LABELS.NAME}</span>}
            rules={[...RULE_VALIDATE]}
            className='mb-4'
          >
            <Input
              prefix={
                <span className='text-[#00000073]'>
                  <UserOutlined />
                </span>
              }
              placeholder={PLACEHOLDERS.NAME}
              size='large'
              className='h-12'
            />
          </Form.Item>

          <Form.Item
            name='password'
            label={<span className='leading-8'>{LABELS.PASSWORD}</span>}
            rules={[...RULE_VALIDATE]}
            className='mb-10'
          >
            <Input.Password
              prefix={
                <span className='text-[#00000073]'>
                  <LockOutlined />
                </span>
              }
              placeholder={PLACEHOLDERS.PASSWORD}
              size='large'
              className='h-12'
            />
          </Form.Item>

          <Form.Item className='mb-10'>
            <Button
              type='primary'
              htmlType='submit'
              className='w-full h-[48px]'
              size='large'
              loading={loading}
            >
              ログイン
            </Button>
          </Form.Item>

          <div>
            <p className='leading-[24px]'>
              パスワードが分からない場合は、
              <br />
              上位管理者にリセットを依頼してください
            </p>
          </div>
        </Form>
      </Card>
    </div>
  )
}

export default Login
