import { CATEGORY_MENUS_ITEMS } from '@/constants/routesElement'
import PrivateLayout from '@/layouts/PrivateLayout'
import { Button, Input, DatePicker, Table } from 'antd'
import { formatTime } from '@/utils/dateTime'
import { DownOutlined } from '@ant-design/icons'
import { ColumnsType } from 'antd/es/table'
import Select from '@/components/Select'
import { useState } from 'react'
import LogModal from './components/LogModal'
import {
  IApiEndpointListResponse,
  IApiHistory,
  IApiHistoryListResponse,
  IGetTopLogParams
} from '@/types/top'
import topLogApi from '@/services/internal/modules/top'
import { useNotification } from '@/hooks/useNotification'
import { useFetch } from '@/hooks/useFetch'
import dayjs from 'dayjs'
import { EMPTY_MARK, SORT_DESC } from '@/constants/common'
const { RangePicker } = DatePicker

const Dashboard = () => {
  //loading
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  //params
  const [limit, setLimit] = useState(20)
  const [adminAPI, setAdminAPI] = useState('')
  const [adminId, setAdminId] = useState('')
  const [fromDate, setFromDate] = useState(dayjs().subtract(30, 'days'))
  const [toDate, setToDate] = useState(dayjs())

  //data
  const [topLogList, setTopLogList] = useState<IApiHistoryListResponse>()
  const [endpoints, setEndpoints] = useState<IApiEndpointListResponse>([])
  //modal
  const [logApiHistory, setlogApiHistory] = useState<
    | {
        type: 'request' | 'response'
        data?: IApiHistory | undefined
      }
    | undefined
  >(undefined)

  const { showNotification } = useNotification()

  const { isFetching: isFetchEndpointList } = useFetch<IApiEndpointListResponse>({
    fetcher: (): Promise<IApiEndpointListResponse> => topLogApi.getEndpointList(),
    handleResponse: (res: IApiEndpointListResponse) => {
      setEndpoints(res)
      return res
    },
    dependencies: []
  })

  const { isFetching: isFetchTopLogList, fetch: fetchTopLogList } =
    useFetch<IApiHistoryListResponse>({
      fetcher: (params): Promise<IApiHistoryListResponse> => {
        const payload = params as IGetTopLogParams
        const [method, path] = (adminAPI || '').split(' ')
        return topLogApi.getTopLogList({
          limit: limit,
          token: payload?.token,
          token_timeline: payload?.token_timeline,
          order: SORT_DESC,
          started_at: dayjs(fromDate).format('YYYY-MM-DD'),
          ended_at: dayjs(toDate).format('YYYY-MM-DD'),
          admin_id: adminId,
          ...(adminAPI ? { method, path } : {})
        })
      },
      handleResponse: (res: IApiHistoryListResponse) => {
        setTopLogList(res)
        return res
      },
      dependencies: [limit]
    })

  const handleLoadMore = async () => {
    if (!topLogList?.continuationToken || !topLogList?.data || isLoadingMore) return

    const currentData = [...topLogList.data]

    try {
      setIsLoadingMore(true)

      const [method, path] = (adminAPI || '').split(' ')
      const result = await topLogApi.getTopLogList({
        limit: limit,
        token: topLogList.continuationToken,
        token_timeline: topLogList?.token_timeline,
        order: SORT_DESC,
        started_at: dayjs(fromDate).format('YYYY-MM-DD'),
        ended_at: dayjs(toDate).format('YYYY-MM-DD'),
        admin_id: adminId,
        ...(adminAPI ? { method, path } : {})
      })

      setTopLogList({
        ...result,
        data: [...currentData, ...result.data]
      })
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoadingMore(false)
    }
  }

  const columns: ColumnsType<IApiHistory> = [
    {
      title: '実行日時',
      dataIndex: 'created_at',
      key: 'created_at',
      width: '12%',
      render: (_, record) => (
        <span>
          {formatTime({
            time: record.execute_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: 'アカウントID',
      dataIndex: 'admin_id',
      key: 'admin_id',
      width: '12%',
      render: (_, record) => {
        return record.admin_id ? (
          <span>{record.admin_id}</span>
        ) : (
          <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
        )
      }
    },
    {
      title: 'API',
      dataIndex: 'api_name',
      key: 'api_name',
      width: '25%',
      render: (_, record) => {
        return (
          <span>
            {endpoints.find((endpoint) => endpoint.path === record.path)?.name || record.path}
          </span>
        )
      }
    },
    {
      title: '実行結果',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => {
        return (
          <div
            className={`flex items-center gap-2 ${record.status === 'success' ? 'text-[#27AE60]' : 'text-text-danger'}`}
          >
            <p
              className={`w-2 h-2 rounded-full ${record.status === 'success' ? 'bg-[#27AE60]' : 'bg-text-danger'}`}
            />
            <span className='capitalize'>{record.status}</span>
          </div>
        )
      }
    },
    {
      title: 'リクエスト',
      dataIndex: 'request',
      key: 'request',
      render: (_, record) => {
        return (
          <span
            onClick={() =>
              setlogApiHistory({
                type: 'request',
                data: record
              })
            }
            className='cursor-pointer'
          >
            {JSON.stringify(record.request, null, 2)}
          </span>
        )
      },
      ellipsis: true
    },
    {
      title: 'レスポンス',
      dataIndex: 'response',
      key: 'response',
      render: (_, record) => {
        return (
          <span
            onClick={() =>
              setlogApiHistory({
                type: 'response',
                data: record
              })
            }
            className='cursor-pointer'
          >
            {JSON.stringify(record.response, null, 2)}
          </span>
        )
      },
      ellipsis: true
    }
  ]

  const onExport = async () => {
    setIsDownloading(true)
    try {
      if (!fromDate || !toDate || !dayjs(fromDate).isValid() || !dayjs(toDate).isValid()) {
        showNotification({
          type: 'error',
          description: '実行期間（開始日と終了日）を選択してください。',
          className: 'whitespace-pre-wrap !w-[406px]'
        })
        return
      }

      const [method, path] = adminAPI.split(' ')
      const response = await topLogApi.exportAdminApiLog({
        admin_id: adminId,
        started_at: dayjs(fromDate).format('YYYY-MM-DD'),
        ended_at: dayjs(toDate).format('YYYY-MM-DD'),
        order: SORT_DESC,
        ...(adminAPI ? { method, path } : {})
      })
      if (response.blob_url) {
        window.open(response.blob_url, '_blank')
      }
    } catch (error) {
      if ((error as { status?: number }).status === 400) {
        showNotification({
          type: 'error',
          description: 'CSVにエクスポート可能なデータが見つかりませんでした。',
          className: 'whitespace-pre-wrap'
        })
        return
      }
      const constraintsMessage =
        (
          error as { response?: { data?: { errors?: { constraints?: Record<string, string> }[] } } }
        )?.response?.data?.errors?.flatMap((err) => Object.values(err.constraints || {})) || []

      if (constraintsMessage.length > 0) {
        showNotification({
          type: 'error',
          description: constraintsMessage.join('\n'),
          className: 'whitespace-pre-wrap'
        })
      }

      setIsDownloading(false)
    } finally {
      setIsDownloading(false)
    }
  }

  const onSearch = async () => {
    if (!fromDate || !toDate || !dayjs(fromDate).isValid() || !dayjs(toDate).isValid()) {
      showNotification({
        type: 'error',
        description: '実行期間（開始日と終了日）を選択してください。',
        className: 'whitespace-pre-wrap !w-[406px]'
      })
      return
    }

    await fetchTopLogList()
  }
  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: '管理者サイトTOP' }]
      }}
      loading={isFetchEndpointList || isFetchTopLogList || isLoadingMore}
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[0].key]}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'> 管理者API実行ログ</h1>
        <Button
          type='primary'
          size='large'
          className='py-3 px-6 leading-6 h-[48px]'
          onClick={onExport}
          loading={isDownloading}
        >
          一覧をCSVに出力
        </Button>
      </div>
      <h4 className='mb-4 text-base font-bold'>検索</h4>
      <div className='mb-8 flex items-center gap-y-2 gap-x-6 flex-wrap'>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>実行日:</span>
          <RangePicker
            allowEmpty
            className='h-10 w-[280px]'
            defaultValue={[fromDate, toDate]}
            onChange={(_, dateString) => {
              setFromDate(dayjs(dateString[0]))
              setToDate(dayjs(dateString[1]))
            }}
          />
        </div>
        <div className='flex items-center'>
          <span className='text-[14px] mr-3'>アカウントID:</span>
          <Input
            placeholder='アカウントID'
            size='large'
            className='w-56 text-sm h-10'
            onChange={(e) => setAdminId(e.target.value)}
          />
        </div>
        <div className='flex items-center justify-end'>
          <div className='mr-3'>API:</div>
          <Select
            width={280}
            size='large'
            placeholder='API'
            onChange={(value) => setAdminAPI(value)}
            defaultValue=''
            options={[
              { label: '-', value: '' },
              ...endpoints.map((endpoint) => ({
                label: endpoint.name,
                value: endpoint.method + ' ' + endpoint.path
              }))
            ]}
          />
        </div>

        <Button
          color='primary'
          variant='outlined'
          size='large'
          className='text-sm'
          onClick={onSearch}
        >
          検索{''}
        </Button>
      </div>
      <div className='flex items-center justify-end mb-4'>
        <div className='mr-6'>表示件数:</div>
        <Select
          width={130}
          size='large'
          placeholder='~10'
          onChange={(value) => setLimit(value)}
          options={[
            { value: 10, label: '10' },
            { value: 20, label: '20' },
            { value: 50, label: '50' },
            { value: 100, label: '100' },
            { value: 500, label: '500' },
            { value: 1000, label: '1000' }
          ]}
          defaultValue={20}
        />
      </div>
      <Table
        className='custom-ant-table'
        columns={columns}
        pagination={false}
        dataSource={topLogList?.data}
        rowKey='id'
        scroll={{ x: 900 }}
        footer={() =>
          topLogList?.hasMoreResults && (
            <div className='bg-white text-center py-2 px-4'>
              <Button
                disabled={isFetchTopLogList || isLoadingMore}
                type='link'
                icon={<DownOutlined />}
                iconPosition='end'
                onClick={handleLoadMore}
                loading={isLoadingMore}
              >
                もっと見る
              </Button>
            </div>
          )
        }
      />

      <LogModal
        open={!!logApiHistory}
        onClose={() => setlogApiHistory(undefined)}
        data={logApiHistory}
        endpointName={
          endpoints.find((endpoint) => endpoint.path === logApiHistory?.data?.path)?.name || ''
        }
      />
    </PrivateLayout>
  )
}

export default Dashboard
