import React from 'react'
import { Modal, List } from 'antd'

import { formatTime } from '@/utils/dateTime'
import { IApiHistory } from '@/types/top'
import { EMPTY_MARK } from '@/constants/common'

interface RequestLogModalProps {
  open: boolean
  onClose: () => void
  data?: {
    type: 'request' | 'response'
    data?: IApiHistory | undefined
  }
  endpointName?: string
}

const RequestLogModal: React.FC<RequestLogModalProps> = ({ open, onClose, data, endpointName }) => {
  const pointHistory = [
    {
      label: '実行日時',
      value: (
        <span>
          {formatTime({
            time: data?.data?.execute_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          }) || '-'}
        </span>
      )
    },
    {
      label: 'API',
      value: <span className='whitespace-pre-wrap'>{endpointName}</span>
    },
    {
      label: 'アカウントID',
      value: data?.data?.admin_id ? (
        <span>{data.data.admin_id}</span>
      ) : (
        <span className='px-3 text-[25px]'>{EMPTY_MARK}</span>
      )
    }
  ]

  return (
    <Modal
      open={open}
      onClose={onClose}
      maskClosable={false}
      onCancel={onClose}
      title={data?.type === 'request' ? 'リクエストログ' : 'レスポンスログ'}
      width={676}
      centered
      footer={null}
    >
      <div className='border border-solid border-border-description rounded-lg mt-4'>
        <List
          itemLayout='horizontal'
          dataSource={pointHistory}
          renderItem={(history) => (
            <List.Item className='!p-0'>
              <div className='flex w-full'>
                <span className='w-1/2 bg-layout-background py-[9px] px-4 text-[#000000A6] border-r border-solid border-border-description flex items-center'>
                  {history.label}
                </span>
                <div className='w-1/2 py-[9px] px-4'>{history.value}</div>
              </div>
            </List.Item>
          )}
        />
      </div>

      <div className='w-full h-9 bg-[#EAD1C5] pt-[6px] px-4 mt-4'>
        <span>{data?.type === 'request' ? 'リクエスト' : 'レスポンス'}</span>
      </div>
      <div className='overflow-y-auto max-h-96 border border-solid border-border-description py-[10px] pl-[20px] pr-[6px]'>
        <pre className='whitespace-pre-wrap overflow-auto'>
          {data?.data ? JSON.stringify(data.data[data.type], null, 4) : ''}
        </pre>
      </div>
    </Modal>
  )
}

export default RequestLogModal
