import { useMemo, useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { Button, Tabs } from 'antd'
import PrivateLayout from '@/layouts/PrivateLayout'
import lotteryApi from '@/services/internal/modules/lottery'
import {
  ILotteryItem,
  IGetLotteryApplicationsResponse,
  IGetLotteryAliasResponse
} from '@/types/lottery'
import { CATEGORY_MENUS_ITEMS, routePaths } from '@/constants/routesElement'
import { ProfileOutlined } from '@ant-design/icons'
import { APPLICATIONS, DETAIL } from '../contstant'
import LotteryDescription from '../components/LotteryDescription'
import LotteryApplications from '../components/LotteryApplications'
import UploadWinnersModal from '../components/UploadWinnersModal'

const LotteryDetail = () => {
  const { id } = useParams<{ id: string }>()
  const location = useLocation()
  const navigate = useNavigate()
  const query = useMemo(() => new URLSearchParams(location.search), [location.search])
  const activeTab = query.get('tab') === APPLICATIONS ? APPLICATIONS : DETAIL
  const [isOpenUploadWinnersModal, setIsOpenUploadWinnersModal] = useState(false)

  // State for detail
  const [lotteryDetail, setLotteryDetail] = useState<ILotteryItem | null>(null)
  const [isLoadingDetail, setIsLoadingDetail] = useState(false)

  // State for applications
  const [lotteryApplications, setLotteryApplications] = useState<
    IGetLotteryApplicationsResponse[] | null
  >(null)
  const [isLoadingApplications, setIsLoadingApplications] = useState(false)

  // state for alias
  const [lotteryAlias, setLotteryAlias] = useState<IGetLotteryAliasResponse | null>(null)
  const [isLoadingAlias, setIsLoadingAlias] = useState(false)

  if (!query.get('tab')) {
    navigate({ search: '?tab=detail' }, { replace: true })
  }

  const handleFetchDetail = async () => {
    setIsLoadingDetail(true)
    setIsLoadingAlias(true)

    try {
      const lotteryApplicationsRes = await lotteryApi.getLotteryDetail(id!)
      const lotteryAliasRes = await lotteryApi.getLotteryAliasById(id!)
      setLotteryDetail(lotteryApplicationsRes)
      setLotteryAlias(lotteryAliasRes)
    } finally {
      setIsLoadingDetail(false)
      setIsLoadingAlias(false)
    }
  }

  const handleFetchApplications = async () => {
    setIsLoadingApplications(true)
    try {
      const res = await lotteryApi.getLotteryApplications(id!)
      setLotteryApplications(res)
    } finally {
      setIsLoadingApplications(false)
    }
  }

  useEffect(() => {
    if (!id) return
    if (activeTab === DETAIL) {
      handleFetchDetail()
    } else if (activeTab === APPLICATIONS) {
      handleFetchApplications()
    }
  }, [id, activeTab])

  const handleTabChange = (key: string) => {
    navigate({ search: `?tab=${key}` }, { replace: true })
  }

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: '抽選一覧', href: routePaths.lottery.list }, { title: '抽選詳細' }],
        className: 'pt-4 pb-8 !mb-0 bg-layout-background '
      }}
      mainClassName='pt-0'
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[3].key]}
      loading={activeTab === DETAIL ? isLoadingDetail || isLoadingAlias : isLoadingApplications}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'>抽選詳細</h1>
      </div>
      <div className='relative'>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          tabBarStyle={{ width: '630px', maxWidth: 'calc(100%-400px)' }}
          items={[
            {
              key: DETAIL,
              label: '抽選詳細',
              children: lotteryDetail ? (
                <LotteryDescription
                  lotteryDetail={lotteryDetail}
                  lotteryAliasDetail={lotteryAlias}
                  lotteryId={id || ''}
                />
              ) : (
                <div className='flex items-center text-[#6F6F6F] text-[20px] mt-8'>
                  <ProfileOutlined />
                  <span className='text-sm ml-2 font-normal'>
                    選択中の企業には現在抽選が存在しません
                  </span>
                </div>
              )
            },
            {
              key: APPLICATIONS,
              label: '応募者一覧',
              children: lotteryApplications ? (
                <LotteryApplications lotteryApplicationsList={lotteryApplications} />
              ) : (
                <div className='flex items-center text-[#6F6F6F] text-[20px] mt-8'>
                  <ProfileOutlined />
                  <span className='text-sm ml-2 font-normal'>応募者はまだいません</span>
                </div>
              )
            }
          ]}
        />
        {activeTab === APPLICATIONS && (
          <Button
            color='primary'
            variant='outlined'
            size='large'
            className='text-sm absolute right-0 top-0'
            onClick={() => setIsOpenUploadWinnersModal(true)}
          >
            当選者をアップロード
          </Button>
        )}
      </div>
      <UploadWinnersModal
        lotteryId={id || ''}
        open={isOpenUploadWinnersModal}
        onClose={() => setIsOpenUploadWinnersModal(false)}
        onSuccess={handleFetchApplications}
      />
    </PrivateLayout>
  )
}

export default LotteryDetail
