import { useState } from 'react'
import PrivateLayout from '@/layouts/PrivateLayout'
import Select from '@/components/Select'
import { CATEGORY_MENUS_ITEMS, routePaths } from '@/constants/routesElement'
import { Button, Table, Dropdown, Modal } from 'antd'
import { MoreOutlined, ProfileOutlined, WarningFilled } from '@ant-design/icons'
import lotteryApi from '@/services/internal/modules/lottery'
import { ILotteryItem, IGetLotteryListResponse } from '@/types/lottery'
import { useCompaniesList } from '@/hooks/useCompaniesList'
import { useFetch } from '@/hooks/useFetch'
import { formatTime } from '@/utils/dateTime'
import { useNavigate } from 'react-router-dom'
import { useNotification } from '@/hooks/useNotification'
import { parseErrorDetails } from '@/utils/errorHandling'

const LotteryList = () => {
  const navigate = useNavigate()
  const [selectedCompanyId, setSelectedCompanyId] = useState('C0000')
  const [lotteryList, setLotteryList] = useState<IGetLotteryListResponse>([])
  const { showNotification } = useNotification()
  const { companiesList, isFetching: isFetchingCompanies } = useCompaniesList()

  const { isFetching: isFetchingLottery } = useFetch<IGetLotteryListResponse>({
    fetcher: (): Promise<IGetLotteryListResponse> => {
      return lotteryApi.getLotteryList(selectedCompanyId, { limit: 1000 })
    },
    handleResponse: (res: IGetLotteryListResponse) => {
      // Sort by updated_at DESC
      const sorted = [...res].sort((a, b) => {
        const dateA = new Date(a.updated_at).getTime()
        const dateB = new Date(b.updated_at).getTime()
        return dateB - dateA
      })
      setLotteryList(sorted)
      return sorted
    },
    handleError: () => {
      setLotteryList([])
      return []
    },
    dependencies: [selectedCompanyId]
  })

  const handleDelete = async (record: ILotteryItem) => {
    Modal.confirm({
      icon: null,
      title: (
        <div>
          <WarningFilled className='text-2xl text-text-danger' />
          <span className='text-text-danger ml-2.5'>警告</span>
        </div>
      ),
      content: (
        <p className='text-base'>{`${record.lottery_name}を本当に削除してもよろしいですか？`}</p>
      ),
      okText: `削除 `,
      okType: 'danger',
      cancelText: 'やめる',
      width: 500,
      okButtonProps: {
        className: '!bg-theme-error-primary !text-white hover:!bg-red-600 hover:!text-white'
      },
      onOk: async () => {
        try {
          await lotteryApi.deleteLottery(record.lottery_id)
          showNotification({ message: `${record.lottery_name}を削除しました。` })
          // Refetch and sort after delete
          const res = await lotteryApi.getLotteryList(selectedCompanyId, { limit: 1000 })
          const sorted = [...res].sort((a, b) => {
            const dateA = new Date(a.updated_at).getTime()
            const dateB = new Date(b.updated_at).getTime()
            return dateB - dateA
          })
          setLotteryList(sorted)
        } catch (error: unknown) {
          const { errorMessage } = parseErrorDetails(error)
          showNotification({ message: errorMessage, type: 'error' })
        }
      }
    })
  }

  const handleAction = (action: string, record: ILotteryItem) => {
    if (action === 'delete') {
      handleDelete(record)
    } else if (action === 'update') {
      navigate(`/lottery/edit/${record.lottery_id}`)
    } else if (action === 'duplicate') {
      navigate(`/lottery/duplicate/${record.lottery_id}`)
    }
  }

  const columns = [
    {
      title: '更新日',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 120,
      render: (text: string) => (
        <span>
          {formatTime({
            time: text,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: '抽選名',
      dataIndex: 'lottery_name',
      key: 'lottery_name',
      width: 120
    },
    {
      title: 'ID',
      dataIndex: 'lottery_id',
      key: 'lottery_id',
      width: 120
    },
    {
      title: '企業ID',
      key: 'campaign_company_id',
      width: 100,
      render: (_: unknown, record: ILotteryItem) => record.summary?.campaign_company_id || ''
    },
    {
      title: '開始日時',
      key: 'start_at',
      width: 140,
      render: (_: unknown, record: ILotteryItem) => (
        <span>
          {formatTime({
            time: record.summary.start_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: '終了日時',
      key: 'ended_at',
      width: 140,
      render: (_: unknown, record: ILotteryItem) => (
        <span>
          {formatTime({
            time: record.summary.ended_at,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: '',
      key: 'action',
      width: 40,
      render: (_: unknown, record: ILotteryItem) => (
        <div onClick={(e) => e.stopPropagation()}>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'update',
                  label: '更新',
                  onClick: () => handleAction('update', record)
                },
                {
                  key: 'duplicate',
                  label: '複製',
                  onClick: () => handleAction('duplicate', record)
                },
                {
                  key: 'delete',
                  danger: true,
                  label: '削除',
                  onClick: () => handleAction('delete', record)
                }
              ]
            }}
            trigger={['click']}
            overlayClassName='ant-custom-dropdown'
            dropdownRender={(menu) => (
              <div style={{ minWidth: 160 }} onClick={(e) => e.stopPropagation()}>
                {menu}
              </div>
            )}
          >
            <MoreOutlined
              style={{ fontSize: 20, cursor: 'pointer' }}
              className='text-text-link-blue'
            />
          </Dropdown>
        </div>
      )
    }
  ]

  return (
    <PrivateLayout
      breadcrumb={{ items: [{ title: '抽選一覧' }] }}
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[3].key]}
      loading={isFetchingCompanies || isFetchingLottery}
    >
      <div className='flex items-center justify-between mb-8'>
        <h1 className='font-bold text-[24px] leading-[38px]'>抽選一覧</h1>
        <Button
          type='primary'
          size='large'
          className='py-3 px-11 leading-6 h-[48px]'
          onClick={() => navigate(routePaths.lottery.create)}
        >
          新規作成
        </Button>
      </div>
      <div className='mb-4 flex justify-between items-center gap-5'>
        <div>
          <span className='text-[14px] mr-5'>企業ID:</span>
          <Select
            size='large'
            placeholder='{company_id}'
            width={180}
            onChange={(value) => setSelectedCompanyId(value)}
            defaultValue='C0000'
            options={[
              ...companiesList.map((company) => ({
                label: company.company_id,
                value: company.company_id
              }))
            ]}
          />
        </div>
      </div>
      {lotteryList.length === 0 ? (
        <div className='flex items-center text-[#6F6F6F] text-[20px] mt-8'>
          <ProfileOutlined />
          <span className='text-sm ml-2 font-normal'>選択中の企業には現在抽選が存在しません</span>
        </div>
      ) : (
        <Table
          className='custom-ant-table'
          columns={columns}
          pagination={false}
          dataSource={lotteryList}
          rowKey='lottery_id'
          {...(lotteryList.length > 0 ? { scroll: { x: 900 } } : {})}
          onRow={(record) => ({
            onClick: () => navigate(`/lottery/detail/${record.lottery_id}`)
          })}
        />
      )}
    </PrivateLayout>
  )
}

export default LotteryList
