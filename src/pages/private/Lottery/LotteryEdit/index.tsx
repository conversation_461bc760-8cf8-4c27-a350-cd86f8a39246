import PrivateLayout from '@/layouts/PrivateLayout'
import { CATEGORY_MENUS_ITEMS, routePaths } from '@/constants/routesElement'
import { Form, Input, DatePicker, TimePicker, Radio, Button, Modal, Switch } from 'antd'
import { CloseOutlined, WarningFilled } from '@ant-design/icons'
import classNames from 'classnames'
import { useUploads } from '@/hooks/useUploads'
import Select from '@/components/Select'
import SingleUpload from '@/components/SingleUpload'
import {
  LABELS,
  LOTTERY_TOP,
  LOTTERY_TOP_ABOUT,
  PLACEHOLDERS,
  PREVIEW_OPTIONS,
  VALIDATION_MESSAGES
} from '../contstant.ts'
import { useLotteryFormSubmit } from '../hooks/useLotteryFormSubmit'
import PreviewLotteryTop from '../components/PreviewLotteryTop/index.tsx'
import PreviewLotteryAbout from '../components/PreviewLotteryAbout/index.tsx'
import { useLotteryPreview } from '../hooks/useLotteryPreview.ts'
import { ILotteryFormValues, ILotteryItem } from '@/types/lottery/index.ts'
import { useEffect, useState } from 'react'
import { fieldRequiredMessage } from '@/utils/form.tsx'
import { useNavigate, useParams } from 'react-router-dom'
import lotteryApi from '@/services/internal/modules/lottery.ts'
import { useFetch } from '@/hooks/useFetch.ts'
import dayjs from 'dayjs'

const { TextArea } = Input

const LotteryEdit = () => {
  const [lotteryDetail, setLotteryDetail] = useState<ILotteryItem>()
  const [previewFlag, setPreviewFlag] = useState(false)
  const [pauseFlag, setPauseFlag] = useState(false)
  const [isStartDateDisabled, setIsStartDateDisabled] = useState(false)
  const [isEndDateDisabled, setIsEndDateDisabled] = useState(false)

  const navigate = useNavigate()
  const { id } = useParams()
  const [form] = Form.useForm<ILotteryFormValues>()

  const {
    prizeImageFile,
    prizeImageFileList,
    sameScreenShotImageFile,
    sameScreenShotImageFileList,
    prizeImageOnChange,
    sameScreenShotImageOnChange,
    beforeUpload
  } = useUploads(['prizeImage', 'sameScreenShotImage'])

  const { previewFormValues, previewScreen, setPreviewScreen, handlePreviewForm } =
    useLotteryPreview(form)
  const { isLoadingSubmitForm, handleSubmit, isSubmitError, errorMessage, handleCloseErrorModal } =
    useLotteryFormSubmit(
      prizeImageFile,
      sameScreenShotImageFile,
      id,
      isStartDateDisabled,
      isEndDateDisabled
    )

  const { isFetching: isFetchingLotteryDetail } = useFetch<ILotteryItem>({
    fetcher: () => lotteryApi.getLotteryDetail(id as string),
    handleResponse: (res: ILotteryItem) => {
      if (res) {
        setLotteryDetail(res)
      }
      return res
    },
    handleError: () => {
      navigate(routePaths.lottery.list)
    },
    dependencies: []
  })

  const customizeRequiredMark = (label: React.ReactNode, { required }: { required: boolean }) => (
    <>
      {label}
      {required && <span style={{ color: 'red' }}>*</span>}:
    </>
  )

  const checkStartDateDisabled = (startDate: dayjs.Dayjs | null) => {
    if (!startDate) return false
    const today = dayjs().startOf('day')
    const selectedDate = dayjs(startDate).startOf('day')
    return today.isAfter(selectedDate) || today.isSame(selectedDate)
  }

  const checkEndDateDisabled = (endDate: dayjs.Dayjs | null) => {
    if (!endDate) return false
    const today = dayjs().startOf('day')
    const selectedDate = dayjs(endDate).startOf('day')
    return today.isAfter(selectedDate) || today.isSame(selectedDate)
  }

  const setFormValues = (detail: ILotteryItem) => {
    const startDate = dayjs(detail.summary.start_at)
    const endDate = dayjs(detail.summary.ended_at)

    form.setFieldsValue({
      lottery_name: detail.lottery_name,
      preview_flag: detail.preview_flag === '1' ? true : false,
      pause_flag: detail.pause_flag === '1' ? true : false,
      num_of_winners: detail.num_of_winners,
      campaign_company_id: detail.summary.campaign_company_id,
      prize_name: detail.summary.prize_name,
      target_screenshot_explanation: detail.summary.target_screenshot_explanation,
      application_times: detail.summary.application_times,
      notes: detail.summary.notes || '',
      top_text: detail.summary.top_text || '',
      start_date: startDate,
      start_time: startDate,
      end_date: endDate,
      end_time: endDate,
      ...(detail?.summary?.prize_img_link && {
        prize_img: {
          uid: '-1',
          name: detail?.summary?.prize_img_link.split('/').pop() || 'image',
          url: detail?.summary?.prize_img_link
        }
      }),
      ...(detail?.summary?.same_screenshot_img_link && {
        same_screenshot_img: {
          uid: '-1',
          name: detail?.summary?.same_screenshot_img_link.split('/').pop() || 'image',
          url: detail?.summary?.same_screenshot_img_link
        }
      })
    })

    setIsStartDateDisabled(checkStartDateDisabled(startDate))
    setIsEndDateDisabled(checkEndDateDisabled(endDate))
  }

  const setImageFiles = (detail: ILotteryItem) => {
    if (detail?.summary?.prize_img_link && prizeImageOnChange) {
      prizeImageOnChange({
        file: {
          uid: '-1',
          name: detail?.summary?.prize_img_link.split('/').pop() || 'image',
          url: detail?.summary?.prize_img_link
        },
        fileList: [
          {
            uid: '-1',
            name: detail?.summary?.prize_img_link.split('/').pop() || 'image',
            url: detail?.summary?.prize_img_link
          }
        ]
      })
    }

    if (detail?.summary?.same_screenshot_img_link && sameScreenShotImageOnChange) {
      sameScreenShotImageOnChange({
        file: {
          uid: '-1',
          name: detail?.summary?.same_screenshot_img_link.split('/').pop() || 'image',
          url: detail?.summary?.same_screenshot_img_link
        },
        fileList: [
          {
            uid: '-1',
            name: detail?.summary?.same_screenshot_img_link.split('/').pop() || 'image',
            url: detail?.summary?.same_screenshot_img_link
          }
        ]
      })
    }
  }

  useEffect(() => {
    if (lotteryDetail) {
      setFormValues(lotteryDetail)
      setImageFiles(lotteryDetail)
      setPreviewFlag(lotteryDetail.preview_flag === '1' ? true : false)
      setPauseFlag(lotteryDetail.pause_flag === '1' ? true : false)
      handlePreviewForm()
    }
  }, [lotteryDetail, form])

  return (
    <PrivateLayout
      breadcrumb={{
        items: [{ title: '抽選一覧', href: routePaths.lottery.list }, { title: '抽選編集' }],
        className: 'pt-4 pb-8 !mb-0 bg-layout-background sticky top-0 z-10'
      }}
      mainClassName='pt-0'
      defaultOpenKeys={[CATEGORY_MENUS_ITEMS[3].key]}
      loading={isFetchingLotteryDetail}
    >
      <div className='pb-4 sticky top-[70px] bg-layout-background z-10'>
        <h1 className='font-bold text-[24px] leading-[38px] mb-12'>抽選編集</h1>
        <p>
          <span className='text-text-danger font-bold'>＊</span>は必須項目です。
        </p>
      </div>

      <div className='w-[630px] max-w-[calc(100%-400px)]'>
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSubmit}
          requiredMark={customizeRequiredMark}
          size='large'
        >
          {/* Basic Information */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              基本情報
            </div>

            <Form.Item
              name='campaign_company_id'
              label={LABELS.company_id}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.company_id) }]}
            >
              <Select disabled />
            </Form.Item>

            <div className='relative'>
              <Form.Item
                name='lottery_name'
                label={LABELS.lottery_name}
                rules={[{ required: true, message: fieldRequiredMessage(LABELS.lottery_name) }]}
              >
                <Input className='mt-7' />
              </Form.Item>
              <p className='mb-2.5 text-[#6F6F6F] text-xs leading-5 absolute top-7'>
                管理用の名称です。ユーザー側にはどこにも表示されません。
              </p>
            </div>

            <div>
              <p className='mb-2 custom-label'>
                {LABELS.period}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <div className='flex gap-7'>
                <div className='leading-10 w-11'>{LABELS.start_at}:</div>
                <Form.Item
                  name='start_date'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.start_date_required }]}
                >
                  <DatePicker
                    className='w-[158px]'
                    placeholder={PLACEHOLDERS.start_date}
                    disabled={isStartDateDisabled}
                  />
                </Form.Item>

                <Form.Item
                  name='start_time'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.start_time_required }]}
                >
                  <TimePicker
                    className='w-[158px]'
                    format='HH:mm'
                    placeholder={PLACEHOLDERS.start_time}
                    disabled={isStartDateDisabled}
                  />
                </Form.Item>
              </div>
              <div className='flex gap-7'>
                <div className='leading-10 w-11'>{LABELS.end_at}:</div>
                <Form.Item
                  name='end_date'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.end_date_required }]}
                >
                  <DatePicker
                    className='w-[158px]'
                    placeholder={PLACEHOLDERS.start_date}
                    disabled={isEndDateDisabled}
                  />
                </Form.Item>

                <Form.Item
                  name='end_time'
                  rules={[{ required: true, message: VALIDATION_MESSAGES.end_time_required }]}
                >
                  <TimePicker
                    className='w-[158px]'
                    format='HH:mm'
                    placeholder={PLACEHOLDERS.start_time}
                    disabled={isEndDateDisabled}
                  />
                </Form.Item>
              </div>
            </div>

            <div className='flex items-center'>
              <Form.Item
                name='num_of_winners'
                label={LABELS.num_of_winners}
                rules={[
                  { required: true, message: fieldRequiredMessage(LABELS.num_of_winners) },
                  {
                    pattern: /^\d+$/,
                    message: 'only input positive number'
                  }
                ]}
              >
                <Input type='number' min={0} className='w-[210px]' />
              </Form.Item>
              <span className='ml-2 mt-1.5'>人</span>
            </div>

            <div className='flex items-center gap-4 mb-4'>
              <p className='custom-label w-[180px]'>
                {LABELS.application_times}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <Form.Item
                name='application_times'
                rules={[
                  { required: true, message: fieldRequiredMessage(LABELS.application_times) }
                ]}
                initialValue='once_a_day'
                className='mb-0'
              >
                <Radio.Group className='flex gap-4'>
                  <Radio value='once_a_day'>1日に1回まで</Radio>
                  <Radio value='once_in_a_period'>期間中に1回まで</Radio>
                </Radio.Group>
              </Form.Item>
            </div>

            <div className='flex items-center gap-4 mb-4 relative'>
              <p className='custom-label w-[180px]'>
                {LABELS.preview_flag}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <Form.Item
                name='preview_flag'
                rules={[{ required: true, message: fieldRequiredMessage(LABELS.preview_flag) }]}
                initialValue={false}
                className='mb-0'
                valuePropName='checked'
              >
                <Switch
                  onChange={(checked) => {
                    setPreviewFlag(checked)
                    form.setFieldValue('preview_flag', checked)
                  }}
                />
              </Form.Item>
              <span>{previewFlag ? 'プレビューモード' : '通常モード'}</span>
            </div>

            <div className='flex items-center gap-4 mb-4'>
              <p className='custom-label w-[180px]'>
                {LABELS.pause_flag}
                <span className='text-sm text-text-danger'>*</span>:
              </p>
              <Form.Item
                name='pause_flag'
                rules={[{ required: true, message: fieldRequiredMessage(LABELS.pause_flag) }]}
                initialValue={false}
                className='mb-0'
                valuePropName='checked'
              >
                <Switch
                  onChange={(checked) => {
                    setPauseFlag(checked)
                    form.setFieldValue('pause_flag', checked)
                  }}
                />
              </Form.Item>
              <span>{pauseFlag ? '一時停止中' : '解除中'}</span>
            </div>
          </div>

          {/* Images */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              画像
            </div>

            <Form.Item
              name='prize_img'
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.prize_img) }]}
              label={LABELS.prize_img}
              valuePropName='fileList'
              getValueFromEvent={(e) => e?.fileList}
            >
              <SingleUpload
                name='prize_img'
                beforeUpload={beforeUpload}
                data={prizeImageFileList}
                onChange={prizeImageOnChange}
              />
            </Form.Item>

            <Form.Item
              name='same_screenshot_img'
              rules={[
                { required: true, message: fieldRequiredMessage(LABELS.same_screenshot_img) }
              ]}
              label={LABELS.same_screenshot_img}
              valuePropName='fileList'
              getValueFromEvent={(e) => e?.fileList}
            >
              <SingleUpload
                name='same_screenshot_img'
                beforeUpload={beforeUpload}
                data={sameScreenShotImageFileList}
                onChange={sameScreenShotImageOnChange}
              />
            </Form.Item>
          </div>

          {/* title lottery_top and lottery_top@about text */}
          <div className='mb-8'>
            <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
              トップ画面・詳細ポップアップ本文
            </div>

            <div className='relative'>
              <Form.Item name='top_text' label={LABELS.top_text}>
                <TextArea rows={4} className='mt-7' />
              </Form.Item>
              <span className='mb-1.5 text-text-sub-note text-xs inline-block absolute top-7'>{`改行は”<br>”で記載してください。`}</span>
            </div>

            <Form.Item
              name='prize_name'
              label={LABELS.prize_name}
              rules={[{ required: true, message: fieldRequiredMessage(LABELS.prize_name) }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name='target_screenshot_explanation'
              label={LABELS.target_screenshot_explanation}
              rules={[
                {
                  required: true,
                  message: fieldRequiredMessage(LABELS.target_screenshot_explanation)
                }
              ]}
            >
              <TextArea rows={4} />
            </Form.Item>

            <Form.Item name='notes' label={LABELS.notes}>
              <TextArea rows={4} />
            </Form.Item>
          </div>

          {/* Submit Button */}
          <Button
            size='large'
            type='primary'
            htmlType='button'
            onClick={() => form.submit()}
            loading={isLoadingSubmitForm}
            disabled={isLoadingSubmitForm}
            className='w-[156px]'
          >
            更新{' '}
          </Button>
        </Form>
      </div>

      {/* Preview  */}
      <div className='fixed top-[112px] right-8 z-20'>
        <div className='flex items-center gap-6 justify-end'>
          <div>
            <span className='text-sm inline-block mr-4'>プレビュー:</span>
            <Select
              placeholder='画面名'
              size='large'
              defaultValue={LOTTERY_TOP}
              className='w-[180px]'
              onChange={(value) => setPreviewScreen(value)}
              options={PREVIEW_OPTIONS}
            />
          </div>
          <Button
            color='primary'
            variant='outlined'
            size='large'
            className='text-sm'
            onClick={handlePreviewForm}
          >
            プレビュー更新
          </Button>
        </div>

        <div
          className={classNames(
            'mt-6 w-[345px] bg-white rounded-lg shadow-lg overflow-hidden justify-self-end border-2 border-[#BDBDBD]'
          )}
        >
          <div className='p-4 border-b border-[#f0f0f0] relative'>
            <p className='text-sm text-center break-all'>抽選チャレンジ</p>
            <CloseOutlined className='text-sm text-[#999] absolute right-2 top-1/2 -translate-y-1/2' />
          </div>
          <div
            className={classNames(
              'h-[600px] max-h-[calc(100vh-250px)] overflow-auto p-3 pb-6 flex flex-col justify-between',
              { 'overflow-hidden relative': previewScreen === LOTTERY_TOP_ABOUT }
            )}
          >
            {previewScreen === LOTTERY_TOP ? (
              <PreviewLotteryTop
                prizeImg={prizeImageFile || form.getFieldValue('prize_img')?.url || null}
                sameScreenshotLink={
                  sameScreenShotImageFile || form.getFieldValue('same_screenshot_img')?.url || null
                }
                topText={previewFormValues?.top_text || ''}
                numOfWinners={previewFormValues?.num_of_winners || ''}
                prizeName={previewFormValues?.prize_name || ''}
                startLottery={
                  previewFormValues?.start_date && previewFormValues?.start_time
                    ? previewFormValues.start_date.toDate().toISOString()
                    : ''
                }
                endLottery={
                  previewFormValues?.end_date && previewFormValues?.end_time
                    ? previewFormValues.end_date.toDate().toISOString()
                    : ''
                }
                applicationTimes={previewFormValues?.application_times || ''}
                targetScreenshotExplanation={previewFormValues?.target_screenshot_explanation || ''}
              />
            ) : (
              <PreviewLotteryAbout
                prizeImg={prizeImageFile || form.getFieldValue('prize_img')?.url || null}
                sameScreenshotLink={
                  sameScreenShotImageFile || form.getFieldValue('same_screenshot_img')?.url || null
                }
                topText={previewFormValues?.top_text || ''}
                numOfWinners={previewFormValues?.num_of_winners || ''}
                prizeName={previewFormValues?.prize_name || ''}
                startLottery={
                  previewFormValues?.start_date && previewFormValues?.start_time
                    ? previewFormValues.start_date.toDate().toISOString()
                    : ''
                }
                endLottery={
                  previewFormValues?.end_date && previewFormValues?.end_time
                    ? previewFormValues.end_date.toDate().toISOString()
                    : ''
                }
                applicationTimes={previewFormValues?.application_times || ''}
                targetScreenshotExplanation={previewFormValues?.target_screenshot_explanation || ''}
                notes={previewFormValues?.notes || ''}
              />
            )}
          </div>
        </div>
      </div>

      <Modal
        open={isSubmitError}
        onCancel={handleCloseErrorModal}
        title={
          <div>
            <WarningFilled className='text-2xl text-text-danger' />
            <span className='text-text-danger ml-2.5'>{errorMessage.title}</span>
          </div>
        }
        footer={[
          <Button
            key={1}
            color='primary'
            variant='outlined'
            size='large'
            className='text-sm'
            onClick={handleCloseErrorModal}
          >
            閉じる
          </Button>
        ]}
        centered
      >
        <p className='text-base whitespace-pre-line'>{errorMessage.message}</p>
      </Modal>
    </PrivateLayout>
  )
}

export default LotteryEdit
