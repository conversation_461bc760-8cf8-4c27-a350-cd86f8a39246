import { Table, TableProps } from 'antd'
import { IGetLotteryApplicationsResponse } from '@/types/lottery'
import { formatTime } from '@/utils/dateTime'

interface LotteryApplicationsProps {
  lotteryApplicationsList: IGetLotteryApplicationsResponse[] | null
}

const LotteryApplications: React.FC<LotteryApplicationsProps> = ({ lotteryApplicationsList }) => {
  const columns: TableProps<IGetLotteryApplicationsResponse>['columns'] = [
    {
      title: '応募日時',
      dataIndex: 'applied_at',
      key: 'applied_at',
      width: 200,
      render: (text: string) => (
        <span>
          {formatTime({
            time: text,
            format: 'YYYY/MM/DD HH:mm:ss'
          })}
        </span>
      )
    },
    {
      title: 'Line ID',
      dataIndex: 'member_line_id',
      key: 'member_line_id'
    },
    {
      title: 'PC ID',
      dataIndex: 'peer_conne_id',
      key: 'peer_conne_id'
    },
    {
      title: 'スクリーンショット',
      dataIndex: ['screenshot', 'url'],
      key: 'url',
      render: (url: string) => <a href={url}>URL</a>
    },
    {
      title: '結果',
      dataIndex: 'result',
      key: 'result',
      width: 100,
      render: (result: string) => (
        <span
          style={{
            color: result === 'win' ? '#27AE60' : '#333'
          }}
        >
          {result === 'tbd' ? 'TBD' : result === 'win' ? '当選' : result}
        </span>
      )
    }
  ]

  return (
    <div className='mt-4'>
      <Table
        className='custom-ant-table'
        columns={columns}
        dataSource={lotteryApplicationsList || []}
        pagination={false}
        {...(lotteryApplicationsList || [].length > 0 ? { scroll: { x: 900 } } : {})}
        rowKey={(record) => record.lottery_id}
      />
    </div>
  )
}

export default LotteryApplications
