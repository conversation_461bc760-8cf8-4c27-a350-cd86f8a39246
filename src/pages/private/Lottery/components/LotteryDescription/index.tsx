import Select from '@/components/Select'
import { IGetLotteryAliasResponse, ILotteryItem } from '@/types/lottery'
import { convertDateWithFormat } from '@/utils/dateTime'
import { Button, Descriptions, Input, Modal } from 'antd'
import { DescriptionsProps } from 'antd/lib'
import { LOTTERY_TOP, LOTTERY_TOP_ABOUT, PREVIEW_OPTIONS } from '../../contstant'
import { useEffect, useState } from 'react'
import classNames from 'classnames'
import { CloseOutlined, WarningFilled } from '@ant-design/icons'
import PreviewLotteryTop from '../PreviewLotteryTop'
import PreviewLotteryAbout from '../PreviewLotteryAbout'
import lotteryApi from '@/services/internal/modules/lottery'
import { parseErrorDetails } from '@/utils/errorHandling'
import { NOT_FOUND } from '@/constants/status-response'
import { useNavigate } from 'react-router-dom'

interface LotteryDescriptionProps {
  lotteryDetail: ILotteryItem
  lotteryAliasDetail: IGetLotteryAliasResponse | null
  lotteryId: string
}

const LotteryDescription: React.FC<LotteryDescriptionProps> = ({
  lotteryDetail,
  lotteryAliasDetail,
  lotteryId
}) => {
  const navigate = useNavigate()

  const [isErrorSubmitAlias, setIsErrorSubmitAlias] = useState(false)
  const [isLoadingSubmitAlias, setIsLoadingSubmitAlias] = useState(false)
  const [previewScreen, setPreviewScreen] = useState(LOTTERY_TOP)
  const [aliasValue, setAliasValue] = useState(lotteryAliasDetail?.lottery_alias_id || '')
  const [isLinkAlias, setIsLinkAlias] = useState(
    lotteryAliasDetail && Object.keys(lotteryAliasDetail).length > 0
  )
  const [errorMessage, setErrorMessage] = useState<{ title: string; message: string }>({
    title: '',
    message: ''
  })

  const formattedStartLottery = lotteryDetail?.summary?.start_at
    ? convertDateWithFormat(lotteryDetail.summary.start_at, 'YearMonthDateWithoutMinute')
    : ''
  const formattedEndLottery = lotteryDetail?.summary?.ended_at
    ? convertDateWithFormat(lotteryDetail.summary.ended_at, 'YearMonthDateWithoutMinute')
    : ''
  const lotteryDescriptionItems: DescriptionsProps['items'] = [
    {
      key: '1',
      label: '抽選名',
      children: lotteryDetail?.lottery_name
    },
    {
      key: '2',
      label: '抽選ID',
      children: lotteryDetail?.lottery_id
    },
    {
      key: '3',
      label: 'プレビュー設定',
      children: lotteryDetail?.preview_flag === '1' ? 'プレビューモード' : '通常モード'
    },
    {
      key: '4',
      label: '一時停止設定',
      children: lotteryDetail?.pause_flag === '1' ? '一時停止中' : '解除中'
    },
    {
      key: '5',
      label: '応募期間',
      children: `${formattedStartLottery} ~ ${formattedEndLottery}`
    },
    {
      key: '6',
      label: '当選者数',
      children: lotteryDetail?.num_of_winners
    },
    {
      key: '7',
      label: '応募可能頻度',
      children:
        lotteryDetail?.summary?.application_times === 'once_a_day'
          ? '1日に1回まで'
          : '期間中に1回まで'
    },
    {
      key: '8',
      label: '景品内容',
      children: lotteryDetail?.summary?.prize_name
    },
    {
      key: '9',
      label: '景品画像',
      children: (
        <a href={lotteryDetail?.summary?.prize_img_link} className='text-text-link underline'>
          画像リンク
        </a>
      )
    },
    {
      key: '10',
      label: '抽選対象画像サンプル',
      children: (
        <a
          href={lotteryDetail?.summary?.same_screenshot_img_link}
          className='text-text-link underline'
        >
          画像リンク
        </a>
      )
    },
    {
      key: '11',
      label: 'トップ画面本文',
      children: lotteryDetail?.summary?.top_text || '-'
    },
    {
      key: '12',
      label: 'チャレンジ対象画像説明',
      children: lotteryDetail?.summary?.target_screenshot_explanation
    },
    {
      key: '13',
      label: '注意事項',
      children: lotteryDetail?.summary?.notes || '-'
    }
  ]

  const handleSubmitAlias = async () => {
    setIsLoadingSubmitAlias(true)
    try {
      if (isLinkAlias && lotteryAliasDetail) {
        await lotteryApi.unlinkLotteryAlias(lotteryAliasDetail.lottery_alias_id || aliasValue)
        setIsLinkAlias(false)
        setAliasValue('')
      } else {
        const res = await lotteryApi.linkLotteryAlias({
          lotteryId: lotteryId || '',
          lotteryAliasId: aliasValue.trim()
        })
        setIsLinkAlias(true)
        setAliasValue(res.lottery_alias_id)
      }
    } catch (error) {
      setIsErrorSubmitAlias(true)
      const { errorStatus } = parseErrorDetails(error)
      if (errorStatus === NOT_FOUND) {
        setErrorMessage({ title: '入力エラー', message: 'エイリアスが有効ではありません' })
        return
      }
      setErrorMessage({ title: '入力エラー', message: 'エイリアスが入力されていません。' })
    } finally {
      setIsLoadingSubmitAlias(false)
    }
  }

  const handleCloseErrorModal = () => {
    setIsErrorSubmitAlias(false)
    setErrorMessage({ title: '', message: '' })
  }

  useEffect(() => {
    setAliasValue(lotteryAliasDetail?.lottery_alias_id || '')
  }, [lotteryAliasDetail])

  return (
    <div className='pt-4 w-[630px] max-w-[calc(100%-400px)]'>
      <div className='mb-8'>
        <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
          エイリアス
        </div>
        <div className='flex items-center gap-6'>
          <Input
            size='large'
            disabled={Boolean(isLinkAlias)}
            value={aliasValue}
            onChange={(e) => setAliasValue(e.target.value)}
          />
          <Button
            color='primary'
            variant='outlined'
            size='large'
            disabled={isLoadingSubmitAlias}
            loading={isLoadingSubmitAlias}
            className='text-sm'
            onClick={handleSubmitAlias}
          >
            {isLinkAlias ? 'エイリアスを解除' : 'エイリアスを設定'}
          </Button>
        </div>
      </div>

      <div className='mb-8'>
        <div className='border-b-2 border-b-[#1558D64D] text-base leading-[28px] font-bold pb-1 mb-4'>
          抽選詳細
        </div>
        <div className='flex items-center gap-6'>
          <Descriptions
            column={1}
            bordered
            layout='horizontal'
            items={lotteryDescriptionItems}
            className='custom-descriptions-audience'
          />
        </div>
      </div>
      <Button
        type='primary'
        size='large'
        className='py-3 px-9 leading-6 h-[48px]'
        onClick={() => navigate(`/lottery/edit/${lotteryId}`)}
      >
        抽選を編集
      </Button>

      {/* Preview  */}
      <div className='fixed top-[112px] right-8 z-20'>
        <div className='flex items-center gap-6 justify-end'>
          <div>
            <span className='text-sm inline-block mr-4'>プレビュー:</span>
            <Select
              placeholder='画面名'
              size='large'
              defaultValue={LOTTERY_TOP}
              className='w-[180px]'
              onChange={(value) => setPreviewScreen(value)}
              options={PREVIEW_OPTIONS}
            />
          </div>
        </div>

        <div
          className={classNames(
            'mt-6 w-[345px] bg-white rounded-lg shadow-lg overflow-hidden justify-self-end border-2 border-[#BDBDBD]'
          )}
        >
          <div className='p-4 border-b border-[#f0f0f0] relative'>
            <p className='text-sm text-center break-all'>抽選チャレンジ</p>
            <CloseOutlined className='text-sm text-[#999] absolute right-2 top-1/2 -translate-y-1/2' />
          </div>
          <div
            className={classNames(
              'h-[600px] max-h-[calc(100vh-250px)] overflow-auto p-3 pb-6 flex flex-col justify-between',
              { 'overflow-hidden relative': previewScreen === LOTTERY_TOP_ABOUT }
            )}
          >
            {previewScreen === LOTTERY_TOP ? (
              <PreviewLotteryTop
                prizeImg={lotteryDetail?.summary?.prize_img_link}
                sameScreenshotLink={lotteryDetail?.summary?.same_screenshot_img_link}
                topText={lotteryDetail?.summary?.top_text || ''}
                numOfWinners={lotteryDetail?.num_of_winners || ''}
                prizeName={lotteryDetail?.summary?.prize_name || ''}
                startLottery={lotteryDetail?.summary?.start_at || ''}
                endLottery={lotteryDetail?.summary?.ended_at || ''}
                applicationTimes={lotteryDetail?.summary?.application_times || ''}
                targetScreenshotExplanation={
                  lotteryDetail?.summary?.target_screenshot_explanation || ''
                }
              />
            ) : (
              <PreviewLotteryAbout
                prizeImg={lotteryDetail?.summary?.prize_img_link}
                sameScreenshotLink={lotteryDetail?.summary?.same_screenshot_img_link}
                topText={lotteryDetail?.summary?.top_text || ''}
                numOfWinners={lotteryDetail?.num_of_winners || ''}
                prizeName={lotteryDetail?.summary?.prize_name || ''}
                startLottery={lotteryDetail?.summary?.start_at || ''}
                endLottery={lotteryDetail?.summary?.ended_at || ''}
                applicationTimes={lotteryDetail?.summary?.application_times || ''}
                targetScreenshotExplanation={
                  lotteryDetail?.summary?.target_screenshot_explanation || ''
                }
                notes={lotteryDetail?.summary?.notes || ''}
              />
            )}
          </div>
        </div>
      </div>

      <Modal
        open={isErrorSubmitAlias}
        onCancel={handleCloseErrorModal}
        title={
          <div>
            <WarningFilled className='text-2xl text-text-danger' />
            <span className='text-text-danger ml-2.5'>{errorMessage.title}</span>
          </div>
        }
        footer={[
          <Button
            key={1}
            color='primary'
            variant='outlined'
            size='large'
            className='text-sm'
            onClick={handleCloseErrorModal}
          >
            閉じる
          </Button>
        ]}
        centered
      >
        <p className='text-base whitespace-pre-line'>{errorMessage.message}</p>
      </Modal>
    </div>
  )
}

export default LotteryDescription
