/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-irregular-whitespace */
import React, { useEffect, useState, useCallback } from 'react'
import { Modal, Button, Form, Alert, Upload } from 'antd'
import { WarningFilled } from '@ant-design/icons'
import { customizeRequiredMark } from '@/utils/form'
import { RcFile } from 'antd/es/upload'
import type { UploadFile } from 'antd/es/upload/interface'
import { useNotification } from '@/hooks/useNotification'
import FileCsvOutlined from '@/assets/icons/FileCsvOutlined.svg'
import { parseApiErrorMessages, parseErrorDetails } from '@/utils/errorHandling'
import SingleUpload from '@/components/SingleUpload'
import { BAD_REQUEST } from '@/constants/status-response'
import { messages, WINNER_ALREADY_EXISTS } from '../../contstant'
import {
  CONVERTED_ERROR_MESSAGES_RESPONSE,
  ERROR_MESSAGES_RESPONSE,
  FILE_SIZE_LIMIT,
  INVALID_FILE_FORMAT_CSV,
  INVALID_FILE_SIZE
} from '@/constants/common'
import lotteryApi from '@/services/internal/modules/lottery'

interface UploadWinnersModalProps {
  lotteryId: string
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface IFormValues {
  point_users_file: { file: File; fileList: UploadFile<RcFile>[] } | null
}

interface ApiErrorResponse {
  response?: {
    data?: {
      errors?: {
        file_csv?: string[]
      }
    }
  }
}

const UploadWinnersModal: React.FC<UploadWinnersModalProps> = ({
  lotteryId,
  open,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm<IFormValues>()
  const [csvFileList, setCsvFileList] = useState<UploadFile[]>([])
  const [loading, setLoading] = useState(false)
  const [errorList, setErrorList] = useState<string[]>([])
  const [showErrorAlert, setShowErrorAlert] = useState<boolean>(true)
  const [showWarningModal, setShowWarningModal] = useState<boolean>(false)
  const [formDataCache, setFormDataCache] = useState<FormData | null>(null)
  const { showNotification } = useNotification()

  const beforeUpload = useCallback(
    (file: RcFile) => {
      const isCsv = file.type === 'text/csv' || file.name.endsWith('.csv')
      const isFileTooLarge = file.size > FILE_SIZE_LIMIT

      if (!isCsv) {
        showNotification({
          message: INVALID_FILE_FORMAT_CSV,
          type: 'error'
        })
        return Upload.LIST_IGNORE
      }

      if (isFileTooLarge) {
        showNotification({
          message: INVALID_FILE_SIZE,
          type: 'error'
        })
        return Upload.LIST_IGNORE
      }

      return false
    },
    [showNotification]
  )

  const handleAlertClose = () => setShowErrorAlert(false)

  // Extracted common error handling logic
  const handleApiError = useCallback((errors: unknown) => {
    const { errorStatus, errorCode, errorMessage } = parseErrorDetails(errors)

    if (errorStatus === BAD_REQUEST && errorCode === 'ERR_BAD_REQUEST' && !errorMessage) {
      // Handle CSV file validation errors
      const csvErrors = (errors as ApiErrorResponse)?.response?.data?.errors?.file_csv
      if (csvErrors && Array.isArray(csvErrors)) {
        const message = `${csvErrors.length}件のpeer_conne_idが正しくありません。`
        setErrorList([message])
      }
      return
    }

    if (errorMessage) {
      if (errorMessage === WINNER_ALREADY_EXISTS) {
        setShowWarningModal(true)
        return
      }

      setErrorList([
        errorMessage === ERROR_MESSAGES_RESPONSE.lotteryInvalidColumnName
          ? CONVERTED_ERROR_MESSAGES_RESPONSE.lotteryInvalidColumnName
          : errorMessage
      ])
      return
    }

    // Handle general API errors as fallback
    const errorDetails = parseApiErrorMessages(errors, true)
    setErrorList(errorDetails?.messageArray || [])
  }, [])

  const onSubmit = async (values: IFormValues) => {
    setErrorList([])
    setShowErrorAlert(true)
    setLoading(true)

    try {
      const csvFile = values.point_users_file?.fileList[0].originFileObj
      const formData = new FormData()

      if (csvFile) {
        formData.append('giftee_winners_csv', csvFile)
      }

      // Cache formData for later use in warning modal submission
      setFormDataCache(formData)

      await lotteryApi.createWinners(lotteryId, formData)
      showNotification({ message: messages.success.createWinners })
      form.resetFields()
      onClose()
      onSuccess?.()
    } catch (errors) {
      handleApiError(errors)
    } finally {
      setLoading(false)
    }
  }

  const handleWarningSubmit = async () => {
    setErrorList([])
    setShowErrorAlert(true)
    setLoading(true)

    try {
      if (formDataCache) {
        await lotteryApi.updateWinners(lotteryId, formDataCache)

        showNotification({ message: messages.success.updateWinners })
        form.resetFields()
        setShowWarningModal(false)
        onClose()
        onSuccess?.()
      }
    } catch (errors) {
      handleApiError(errors)
      setShowWarningModal(false)
    } finally {
      setLoading(false)
    }
  }

  const renderErrorAlerts = useCallback(() => {
    if (errorList.length === 0 || !showErrorAlert) return null

    return errorList.map((error, index) => (
      <Alert
        key={index}
        message={error}
        type='error'
        showIcon
        className='mb-4'
        closable
        onClose={handleAlertClose}
      />
    ))
  }, [errorList, showErrorAlert, handleAlertClose])

  useEffect(() => {
    if (open) {
      form.resetFields()
      setErrorList([])
      setCsvFileList([])
      setShowWarningModal(false)
      setFormDataCache(null)
    }
  }, [open, form])

  return (
    <>
      <Modal
        open={showWarningModal}
        onCancel={() => setShowWarningModal(false)}
        maskClosable={false}
        width={630}
        title={
          <div>
            <WarningFilled className='text-2xl text-text-danger' />
            <span className='text-text-danger ml-2.5'>警告</span>
          </div>
        }
        footer={[
          <Button
            disabled={loading}
            key='close'
            className='mt-2'
            onClick={() => setShowWarningModal(false)}
          >
            やめる
          </Button>,
          <Button
            key='submit'
            className='mt-2 !bg-text-danger !text-white hover:!bg-red-600 hover:!text-white'
            type='primary'
            onClick={handleWarningSubmit}
            loading={loading}
            disabled={loading}
          >
            上書きする
          </Button>
        ]}
        centered
      >
        <div className='py-4'>
          {renderErrorAlerts()}
          <p className='text-base'>
            すでに当選者リストが存在します。
            <br />
            この操作によってリストが上書きされますがよろしいでしょうか？
          </p>
        </div>
      </Modal>

      <Modal
        open={open && !showWarningModal}
        onCancel={onClose}
        maskClosable={false}
        title='当選者リストをアップロード'
        className='modal-scroll-wrapper'
        footer={[
          <Button
            disabled={loading}
            key='close'
            color='primary'
            variant='outlined'
            size='large'
            className='mt-2'
            onClick={onClose}
          >
            キャンセル
          </Button>,
          <Button
            key='submit'
            size='large'
            className='mt-2'
            type='primary'
            onClick={() => form.submit()}
            loading={loading}
            disabled={loading || csvFileList.length === 0}
          >
            確定{' '}
          </Button>
        ]}
        width={676}
        centered
      >
        <div className='scroll-modal-content'>
          <div>
            {renderErrorAlerts()}
            <p className='text-[13px] mt-6 mb-4'>
              <span className='text-text-danger'>＊</span>は必須項目です。
            </p>

            <Form
              layout='vertical'
              name='upload_winners_form'
              requiredMark={customizeRequiredMark}
              onFinish={onSubmit}
              form={form}
              className='space-y-4'
            >
              <Form.Item
                label='ファイルをアップロードしてください。'
                required
                name='point_users_file'
                rules={[
                  {
                    required: true
                  }
                ]}
              >
                <SingleUpload
                  name='point_users_file'
                  accept='.csv'
                  beforeUpload={beforeUpload}
                  data={csvFileList}
                  onChange={({ fileList }) => setCsvFileList(fileList)}
                  customIcon={<img src={FileCsvOutlined} alt='FileCsvOutlined' />}
                />
              </Form.Item>
            </Form>

            <div className='px-6 py-3 mt-4 bg-[#FCEFE9] text-[13px]'>
              <p>※CSV形式のファイルを１つアップロードしてください。</p>
              <p>※ヘッダーに次の項目を含めてください。</p>
              <p>　giftee_url, peer_conne_id</p>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default UploadWinnersModal
