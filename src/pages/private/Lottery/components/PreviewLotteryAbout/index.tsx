import { Button } from 'antd'
import { ONCE_A_DAY } from '../../contstant'
import { RcFile } from 'antd/lib/upload'
import { convertDateWithFormat } from '@/utils/dateTime'

interface PreviewLotteryAboutProps {
  prizeImg: RcFile | string | null
  topText: string
  notes: string
  numOfWinners: number | string
  prizeName: string
  startLottery: string
  endLottery: string
  applicationTimes: string
  targetScreenshotExplanation: string
  sameScreenshotLink: RcFile | string | null
}

const PreviewLotteryAbout: React.FC<PreviewLotteryAboutProps> = ({
  prizeImg,
  topText,
  notes,
  numOfWinners,
  prizeName,
  startLottery,
  endLottery,
  applicationTimes,
  targetScreenshotExplanation,
  sameScreenshotLink
}) => {
  const formattedStartLottery = startLottery
    ? convertDateWithFormat(startLottery, 'YearMonthDateWithoutMinute')
    : ''
  const formattedEndLottery = endLottery
    ? convertDateWithFormat(endLottery, 'YearMonthDateWithoutMinute')
    : ''

  return (
    <div className='w-full flex flex-col justify-between static max-h-[100%] overflow-hidden'>
      <div className='text-sm font-normal space-y-6'>
        {prizeImg ? (
          <div className='w-full'>
            <img
              className='rounded'
              alt='Lottery'
              src={
                typeof prizeImg === 'string'
                  ? prizeImg
                  : prizeImg instanceof File
                    ? URL.createObjectURL(prizeImg)
                    : ''
              }
            />
          </div>
        ) : (
          <div className='mx-auto w-full h-[160px] rounded bg-[#e6e6e6]'></div>
        )}
        <div className='space-y-4'>
          <div className='space-y-2'>
            {topText && (
              <p
                dangerouslySetInnerHTML={{
                  __html: topText
                }}
              />
            )}
            <p>
              対象となる画像の登録1枚につき、１口応募できます。応募者の中から抽選で
              <strong>{numOfWinners}</strong>名様に
              <strong>{prizeName}</strong>をプレゼントします。
            </p>
            <p>
              <span className='font-medium'>詳細は</span>
              <span className='text-theme-link cursor-pointer underline decoration-solid'>
                こちら
              </span>
              から
            </p>
          </div>
          <div>
            <h3 className='font-bold mb-1'>応募期間</h3>
            <p className='mb-3'>
              {formattedStartLottery} ～ {formattedEndLottery}
            </p>
            {applicationTimes === ONCE_A_DAY ? (
              <ul>
                <li>※ 1日1回を上限に、期間中は毎日応募可能です。毎日の応募で当選確率もUP！</li>
                <li>
                  ※ 毎日ご応募いただいた場合でも、当キャンペーンのご当選はおひとり様1回となります。
                </li>
              </ul>
            ) : (
              <ul>
                <li>※ 期間中、ご応募はお一人様1回限りとさせていただきます。</li>
              </ul>
            )}
          </div>
          <div>
            <h3 className='font-bold mb-1'>応募資格</h3>
            <ul>
              <li>・ピアコネLINE公式アカウント会員</li>
            </ul>
          </div>
          <div>
            <h3 className='font-bold mb-1'>対象となる画像</h3>
            <p
              dangerouslySetInnerHTML={{
                __html: targetScreenshotExplanation
              }}
            />
          </div>
          {sameScreenshotLink ? (
            <div className='mx-auto h-[208px]'>
              <img
                className='h-full w-auto max-w-full mx-auto object-cover'
                alt='Same screenshot image'
                src={
                  typeof sameScreenshotLink === 'string'
                    ? sameScreenshotLink
                    : sameScreenshotLink instanceof File
                      ? URL.createObjectURL(sameScreenshotLink)
                      : ''
                }
              />
            </div>
          ) : (
            <div className='mx-auto w-[117px] h-[208px] rounded bg-[#e6e6e6]'></div>
          )}
        </div>
      </div>
      <div className='pt-8'>
        <Button
          type='primary'
          className='w-full h-10 text-sm rounded shrink-0 btn-external-primary font-bold'
        >
          画像をアップロードする
        </Button>
      </div>

      {/* lottery modal */}
      <div className='absolute top-0 left-0 z-50 flex items-center justify-center bg-black/40 w-full h-full'>
        <div className='bg-white rounded-xl shadow-xl w-full max-w-[90%] max-h-[80%] flex flex-col'>
          <h3 className='text-theme-primary text-lg text-center font-bold pt-4 pb-2'>応募要項</h3>
          <div className='px-3'>
            <div className='relative'>
              <div className='bg-theme-primary/10 border border-theme-primary/30 rounded max-h-[300px] overflow-x-hidden'>
                <div className='space-y-[18px] p-3.5 text-sm'>
                  <div className='space-y-1'>
                    {topText && (
                      <p
                        dangerouslySetInnerHTML={{
                          __html: topText
                        }}
                      />
                    )}
                    <p>
                      対象となる画像の登録1枚につき、１口応募できます。応募者の中から抽選で
                      <strong>{numOfWinners}</strong>名様に
                      <strong>{prizeName}</strong>をプレゼントします。
                    </p>
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>応募期間</p>
                    <p>
                      {formattedStartLottery} ～ {formattedEndLottery}
                    </p>
                    {applicationTimes === ONCE_A_DAY ? (
                      <ul>
                        <li>
                          ※ 1日1回を上限に、期間中は毎日応募可能です。毎日の応募で当選確率もUP！
                        </li>
                        <li>
                          ※
                          毎日ご応募いただいた場合でも、当キャンペーンのご当選はおひとり様1回となります。
                        </li>
                      </ul>
                    ) : (
                      <ul>
                        <li>※ 期間中、ご応募はお一人様1回限りとさせていただきます。</li>
                      </ul>
                    )}
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>応募資格</p>
                    <ul>
                      <li>・ピアコネＬＩＮＥ公式アカウント会員</li>
                    </ul>
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>対象となる画像</p>
                    <p
                      dangerouslySetInnerHTML={{
                        __html: targetScreenshotExplanation
                      }}
                    />
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>応募方法</p>
                    <ul className='text--indent'>
                      <li>
                        1.
                        対象となる画面をスクリーンショットで撮影、もしくは対象となる写真を撮影します。
                      </li>
                      <li>
                        2.
                        ピアコネＬＩＮＥ公式アカウント下部メニュー内にある「抽選チャレンジ」のボタンをタップします。
                      </li>
                      <li>
                        3.
                        抽選チャレンジ画面内の「画像をアップロードする」をタップし、【1.】で撮影した画像をアップロードします。
                      </li>
                      <li>
                        4.
                        アップロードした画像を確認し、間違いがなければ「この画像でチャレンジする」をタップします。
                      </li>
                      <li>5. 「チャレンジ完了！」と表示されたら、応募完了です。</li>
                    </ul>
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>当選の発表</p>
                    <ul className='text--indent'>
                      <li>
                        ・厳正なる抽選のうえ当選者を決定し、ＬＩＮＥメッセージでの当選通知をもって発表にかえさせていただきます。
                      </li>
                      <li>・ＬＩＮＥメッセージでの当選通知はご当選者のみとなります。</li>
                      <li>
                        ・抽選結果のお問い合わせにはお答えいたしかねますので、ご了承ください。
                      </li>
                    </ul>
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>注意事項</p>
                    <ul
                      className='text--indent'
                      dangerouslySetInnerHTML={{
                        __html: notes
                          .replace(/(\r\n|\r|\n)/g, '<br />')
                          .replace(/\\n/g, '<br />')
                          .replace(/<br\s*\/?>(?!\s*<br\s*\/?)/gi, '<br />')
                      }}
                    />
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>禁止事項について</p>
                    <ul className='text--indent'>
                      <li>
                        ・キャンペーン期間中、ピアコネＬＩＮＥ公式アカウント会員資格を喪失した場合、応募は無効となります。
                      </li>
                      <li>
                        ・同一の応募者が異なる複数のＬＩＮＥアカウントによる応募を行っているなど、運営側が不正と判断した場合は、応募を無効とさせていただきます。
                      </li>
                    </ul>
                  </div>
                  <div className='space-y-1'>
                    <p className='font-bold'>その他注意事項</p>
                    <ul className='text--indent'>
                      <li>・本キャンペーンは株式会社All Rightが主催しています。</li>
                      <li>
                        ・本キャンペーンには、ＬＩＮＥアプリのバージョンを最新の状態にした上で、ご参加ください。
                      </li>
                      <li>
                        ・ＬＩＮＥヤフー株式会社は、本キャンペーンのスポンサーではありません。
                      </li>
                      <li>
                        ・「ＬＩＮＥ」および「ＬＩＮＥ」ロゴは、ＬＩＮＥヤフー株式会社の登録商標です。
                      </li>
                      <li>
                        ・インターネット通信料・接続料はお客様のご負担となります。通信の際の接続トラブルにつきましては、責任を負いかねますので、ご了承ください。
                      </li>
                      <li>
                        ・本キャンペーンはやむを得ない事情により、予告なく中止あるいは内容が変更となる場合がございます。
                      </li>
                      <li>
                        ・第三者の権利侵害等、本キャンペーン中に発生したトラブルにつきましては、当社では責任を負いかねますので、あらかじめご了承ください。
                      </li>
                      <li>・本キャンペーンに応募した後、応募の取消はできません。</li>
                      <li>・獲得された権利は第三者に譲渡できません。</li>
                      <li>
                        ・アプリケーションの動作環境、インターネット接続環境により発生、または当社の判断によって実施するキャンペーン運営の、中断、中止、または内容の変更によって生じるいかなる損害についても、当社が責任を負うものではありません。
                      </li>
                      <li>
                        ・応募された画像に含まれる情報の取扱いに関しては、当社のプライバシーポリシーに従うものとします。
                      </li>
                      <li>
                        ・その他については、ピアコネＬＩＮＥ公式アカウント会員規約およびピアコネプライバシーポリシーが適用されます。
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className='absolute left-[1px] bottom-[1px] right-[1px] h-10 bg-gradient-to-b from-theme-modal-gradient-top/0 via-theme-modal-gradient-middle/50 to-theme-modal-gradient-bottom rounded-bl rounded-br' />
            </div>
          </div>
          <div className='px-3 pb-6 pt-4'>
            <Button
              className='!text-background-main border !border-background-main w-full h-9 rounded'
              variant='outlined'
            >
              <span className='text-theme-primary ml-1 whitespace-nowrap text-xs font-semibold'>
                戻る
              </span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PreviewLotteryAbout
