/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-irregular-whitespace */
import React, { useEffect, useState } from 'react'
import { Modal, Button, Form, Upload, Alert } from 'antd'
import { customizeRequiredMark } from '@/utils/form'
import { InboxOutlined, PlusOutlined } from '@ant-design/icons'
import { RcFile } from 'antd/es/upload'
import type { UploadFile } from 'antd/es/upload/interface'
import pointManagementApi from '@/services/internal/modules/point-management'
import { useNotification } from '@/hooks/useNotification'
import FileCsvOutlined from '@/assets/icons/FileCsvOutlined.svg'
import { LABELS, MESSAGES } from '../../../constant'
import { mapErrorMessage, parseErrorDetails } from '@/utils/errorHandling'
import { FILE_SIZE_LIMIT, INVALID_FILE_FORMAT_CSV, INVALID_FILE_SIZE } from '@/constants/common'
import { BAD_REQUEST, FORBIDDEN } from '@/constants/status-response'
import { messages } from '@/constants/message'

interface PointAddModalProps {
  open: boolean
  onClose: () => void
}

interface IFormValues {
  point_users_file: { file: File; fileList: UploadFile<RcFile>[] } | null
}

const PointAddModal: React.FC<PointAddModalProps> = ({ open, onClose }) => {
  const [form] = Form.useForm<IFormValues>()
  const [loading, setLoading] = useState(false)
  const [errorList, setErrorList] = useState<string[]>([])
  const [showErrorAlert, setShowErrorAlert] = useState<boolean>(true)
  const { showNotification } = useNotification()
  const beforeUpload = (file: RcFile) => {
    const isCsv = file.type === 'text/csv' || file.name.endsWith('.csv')
    const isFileTooLarge = file.size > FILE_SIZE_LIMIT

    if (!isCsv) {
      showNotification({
        message: INVALID_FILE_FORMAT_CSV,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    if (isFileTooLarge) {
      showNotification({
        message: INVALID_FILE_SIZE,
        type: 'error'
      })
      return Upload.LIST_IGNORE
    }

    return false
  }
  const handleAlertClose = () => setShowErrorAlert(false)

  const onSubmit = async (values: IFormValues) => {
    setErrorList([])
    setShowErrorAlert(true)
    setLoading(true)
    try {
      const formData = new FormData()
      values.point_users_file?.fileList.forEach((file) => {
        formData.append('point_users_file', file.originFileObj as RcFile)
      })

      const response = await pointManagementApi.addPoint(formData)
      const hasFailedItems = response.success?.some((item) => item.status === 'failed')

      if (!response.failure.length && !hasFailedItems) {
        showNotification({ message: MESSAGES.createSuccess })
        form.resetFields()
        onClose()
      } else if (response.failure.length || hasFailedItems) {
        const failureMessages = response.failure.map(
          (error) => `${error.file_name}: ${error.message}`
        )
        const failedItems = response.success.filter((item) => item.status === 'failed')
        if (failedItems.length > 0) {
          failureMessages.push(...failedItems.map((item) => `${item.file_name}: ${item.message}`))
        }

        setErrorList(failureMessages)
      }
    } catch (errors) {
      const { errorMessage, errorStatus, errorCode } = parseErrorDetails(errors)
      if (errorStatus === FORBIDDEN) return setErrorList([messages.error.forbiddenAction])

      if (errorStatus === BAD_REQUEST && errorCode === 'ERR_BAD_REQUEST' && errorMessage === '') {
        errorMessage && setErrorList([errorMessage])
        return
      }

      const mappedErrorMessage = mapErrorMessage(errorMessage)
      setErrorList(mappedErrorMessage ? [mappedErrorMessage] : [])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    form.resetFields()
    setErrorList([])
  }, [open])

  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        onCancel={onClose}
        maskClosable={false}
        title='ポイントを追加'
        className='modal-scroll-wrapper'
        footer={[
          <Button
            key='close'
            color='primary'
            variant='outlined'
            size='large'
            className='mt-2'
            onClick={onClose}
          >
            キャンセル
          </Button>,
          <Button
            key='submit'
            size='large'
            className='mt-2'
            type='primary'
            onClick={() => form.submit()}
            loading={loading}
          >
            確定{' '}
          </Button>
        ]}
        width={676}
        centered
      >
        <div className='scroll-modal-content'>
          <div>
            {errorList.length > 0 &&
              showErrorAlert &&
              errorList.map((error, index) => (
                <Alert
                  key={index}
                  message={error}
                  type='error'
                  showIcon
                  className='mb-4 mt-4'
                  closable
                  onClose={handleAlertClose}
                />
              ))}
            <p className='text-[13px] mt-6 mb-2'>
              <span className='text-text-danger'>＊</span>は必須項目です。
            </p>

            <Form
              layout='vertical'
              name='point_users_file'
              requiredMark={customizeRequiredMark}
              onFinish={onSubmit}
              form={form}
            >
              <Form.Item
                label={LABELS.POINT_USERS_FILE}
                required
                colon={false}
                name='point_users_file'
                rules={[
                  {
                    required: true,
                    message: MESSAGES.uploadFileRequired
                  }
                ]}
              >
                <Upload.Dragger
                  beforeUpload={beforeUpload}
                  name='point_users_file'
                  accept='.csv'
                  multiple
                  listType='picture'
                  onChange={(info) => {
                    form.setFieldsValue({
                      point_users_file:
                        info.fileList.length > 0 ? { fileList: info.fileList } : null
                    })
                  }}
                  iconRender={() => <img src={FileCsvOutlined} alt='FileCsvOutlined' />}
                >
                  <p className='ant-upload-drag-icon'>
                    <InboxOutlined />
                  </p>
                  <p className='ant-upload-text !text-sm font-bold'>
                    ドラッグ&ドロップでファイルを追加する
                  </p>
                  <p className='ant-upload-hint text-sm mb-4'>または</p>
                  <Button icon={<PlusOutlined />} size='middle'>
                    ファイルを選択
                  </Button>
                </Upload.Dragger>
              </Form.Item>
            </Form>

            <div className='px-6 py-3 bg-[#FCEFE9] text-[13px]'>
              <p>※ CSV形式のファイルをアップロードしてください。（複数アップロード可能）</p>
              <p>※ ヘッダーに次の項目を含めてください。</p>
              <p>　 peer_conne_id, company_id, event_name, reason, point</p>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default PointAddModal
