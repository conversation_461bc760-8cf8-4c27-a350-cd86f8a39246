import dayjs from 'dayjs'

export type IRequest = {
  body?: {
    index?: number
    redirectId?: string
  }
  params?: {
    [key: string]: string
  }
}

export type IResponse = {
  pk?: string
  type?: string
  unique_key?: string
  lottery_alias_id?: string
  lottery_id?: string
  is_deleted?: string
  withPoint?: boolean
  url_type?: string
  redirect?: string
  browser_type?: string
}

export type IApiHistory = {
  pk: string
  type: string
  unique_key: string
  admin_id?: string
  method: string
  path: string
  status: string
  status_code: number
  request: IRequest
  response: IResponse
  execute_at: string
}

export type IApiEndpointListResponse = {
  name: string
  path: string
  method: string
}[]

export type IApiHistoryListResponse = {
  data: IApiHistory[]
  hasMoreResults: boolean
  continuationToken: string | null
  token_timeline: string
}
export type IExportFileResponse = {
  log_id: string
  blob_url: string
}

export type IGetTopLogParams = {
  started_at?: string | dayjs.Dayjs
  ended_at?: string | dayjs.Dayjs
  limit?: number
  token?: string
  admin_id?: string
  method?: string
  path?: string
  order?: string
  sort_by?: string
  token_timeline?: string
}
