import { CONVERTED_ERROR_MESSAGES_RESPONSE, ERROR_MESSAGES_RESPONSE } from '@/constants/common'

interface ErrorConstraint {
  [key: string]: string
}

interface ErrorChild {
  property: string
  constraints: ErrorConstraint
}

interface ErrorItem {
  property: string
  children?: ErrorChild[]
  constraints?: ErrorConstraint
}

interface ApiErrorResponse {
  errors: ErrorItem[]
}

interface ApiError {
  response?: {
    data?: ApiErrorResponse
  }
}

interface ErrorResult {
  title: string
  message: string
  messageArray?: string[]
}

// parse error details from AxiosError
export const parseErrorDetails = (error: unknown) => {
  const errorStatus = (error as { status?: number }).status
  const errorMessage = (error as { response?: { data?: { errors?: { message?: string } } } })
    ?.response?.data?.errors?.message // in case error reponse has a message field
  const apiEndpoint = (error as { config?: { url?: string } })?.config?.url
  const errorCode = (error as { code?: string })?.code
  const isTimeout =
    errorCode === 'ECONNABORTED' || (error as { message?: string })?.message?.includes('timeout')
  return { errorStatus, errorMessage, apiEndpoint, errorCode, isTimeout }
}

export const parseApiErrorMessages = (error: unknown, asArray = false): ErrorResult => {
  let errorMsg = 'Error'
  let errorTitle = 'エラーが発生しました。もう一度お試しください。'
  let errorMsgArray: string[] = []

  if ((error as ApiError)?.response?.data?.errors) {
    const errors = (error as ApiError).response!.data!.errors
    errorTitle =
      errors
        .map((err: ErrorItem) => {
          if (err.children) {
            return err.children.map((child) => child.property).join(', ')
          }
          return err.property
        })
        .join(', ') || errorTitle

    errorMsgArray = errors
      .map((err: ErrorItem) => {
        if (err.children) {
          return err.children.map((child) => {
            const constraint = Object.values(child.constraints)[0]
            return `${child.property}: ${constraint}`
          })
        }
        if (err.constraints) {
          const constraint = Object.values(err.constraints)[0]
          return [`${err.property}: ${constraint}`]
        }
        return []
      })
      .flat()
      .filter(Boolean)

    errorMsg = errorMsgArray.join('\n')
  }

  const result: ErrorResult = { title: errorTitle, message: errorMsg }

  if (asArray) {
    result.messageArray = errorMsgArray.length > 0 ? errorMsgArray : [errorMsg]
  }

  return result
}

// convert error messages from eng to jp
export const mapErrorMessage = (errorMessage: string | undefined): string | undefined => {
  if (!errorMessage) return undefined

  // Check if the error message matches any of the error messages in ERROR_MESSAGES_RESPONSE
  for (const [key, value] of Object.entries(ERROR_MESSAGES_RESPONSE)) {
    if (errorMessage.includes(value)) {
      return CONVERTED_ERROR_MESSAGES_RESPONSE[
        key as keyof typeof CONVERTED_ERROR_MESSAGES_RESPONSE
      ]
    }
  }

  return errorMessage
}
