import { ThemeConfig } from 'antd'

export const theme: ThemeConfig = {
  token: {
    colorText: '#333333'
  },
  components: {
    Skeleton: {
      gradientFromColor: '#FAFAFA'
    }
  }
}

export const UNKNOWN = 'unknown'

export const ACCESS_TOKEN = 'access_token'
export const USER_NAME = 'user_name'
export const EMPTY_MARK = '-'

//FILE
export const FILE_SIZE_LIMIT = 5 * 1024 * 1024

//MESSAGE
export const INVALID_FILE_FORMAT_CSV = 'CSV形式のファイルをアップロードしてください。'
export const INVALID_FILE_SIZE =
  '画像のサイズが5MBを超えています。画像1個のサイズを5MB以下にしてアップロードしてください。'

//SORT
export const SORT_ASC = 'ASC'
export const SORT_DESC = 'DESC'

export const CONVERTED_ERROR_MESSAGES_RESPONSE = {
  invalidFileFormat: 'CSV形式のファイルをアップロードしてください。',
  invalidColumnName: 'CSVファイルのヘッダー行に次の列名が必要です：peer_conne_id',
  invalidFileData: 'peer_conne_idが正しくありません。',
  invalidFileSize: '5MBを超えるファイルはアップロードできません。',
  lotteryInvalidColumnName: 'CSVファイルのヘッダー行に次の列名が必要です：giftee_url,peer_conne_id'
}

export const ERROR_MESSAGES_RESPONSE = {
  invalidFileFormat: 'Validation failed (field pc_id_file expected type is csv)',
  invalidColumnName: 'The header row should be formatted in the following order: peer_conne_id',
  invalidFileData: 'File invalid data',
  invalidFileSize: 'Validation failed (expected size is less than 5242880)',
  lotteryInvalidColumnName:
    'The header row should be formatted in the following order: giftee_url,peer_conne_id'
}
