import {
  IGetLotteryListParams,
  IGetLotteryListResponse,
  ILotteryCreateResponse,
  ILotteryDeleteResponse,
  ILotteryItem,
  IDuplicateLotteryPayload,
  IGetLotteryApplicationsResponse,
  IGetLotteryAliasResponse,
  IUnlinkLotteryAliasResponse,
  ILinkLotteryAliasResponse,
  ILinkLotteryAliasParams,
  ICreateUpdateWinnersResponse
} from '@/types/lottery'
import { objectToQueryString } from '@/utils/convert'
import service from '..'

const lotteryApi = {
  getLotteryList: (companyId: string, params: Partial<IGetLotteryListParams> = {}) =>
    service.get<IGetLotteryListResponse>(
      `companies/${companyId}/lotteries${objectToQueryString(params)}`
    ),
  getLotteryDetail: (lotteryId: string) => service.get<ILotteryItem>(`/lotteries/${lotteryId}`),
  createLottery: (formData: FormData) =>
    service.upload<ILotteryCreateResponse>('/lotteries', formData),
  deleteLottery: (lotteryId: string) =>
    service.delete<ILotteryDeleteResponse>(`/lotteries/${lotteryId}`),
  updateLottery: (formData: FormData, lotteryId: string) =>
    service.patch<ILotteryCreateResponse>(`/lotteries/${lotteryId}`, formData),
  updatePreviewFlag: (payload: { status: string }, lotteryId: string) =>
    service.put<ILotteryDeleteResponse>(
      `/lotteries/change-status-preview-flag/${lotteryId}`,
      payload
    ),
  updatePauseFlag: (payload: { status: string }, lotteryId: string) =>
    service.put<ILotteryDeleteResponse>(
      `/lotteries/change-status-pause-flag/${lotteryId}`,
      payload
    ),
  duplicateLottery: (payload: IDuplicateLotteryPayload, lotteryId: string) =>
    service.post<ILotteryItem>(`/lotteries/duplicate/${lotteryId}`, payload),
  getLotteryApplications: (lotteryId: string) =>
    service.get<IGetLotteryApplicationsResponse[]>(
      `/lotteries/get-participants/lottery/${lotteryId}`
    ),
  getLotteryAliasById: (lotteryId: string) =>
    service.get<IGetLotteryAliasResponse>(`/lotteries/${lotteryId}/alias`),
  unlinkLotteryAlias: (lotteryAliasId: string) =>
    service.put<IUnlinkLotteryAliasResponse>(
      `/lotteries/unlink-lottery-with-alias/${lotteryAliasId}`
    ),
  linkLotteryAlias: (payload: ILinkLotteryAliasParams) =>
    service.put<ILinkLotteryAliasResponse>(`/lotteries/link-lottery-with-alias`, payload),
  createWinners: (lotteryId: string, formData: FormData) =>
    service.upload<ICreateUpdateWinnersResponse>(
      `/lotteries/store-winners-by-csv/${lotteryId}`,
      formData
    ),
  updateWinners: (lotteryId: string, formData: FormData) =>
    service.updateUpload<ICreateUpdateWinnersResponse>(
      `/lotteries/update-winners-by-csv/${lotteryId}`,
      formData
    )
}

export default lotteryApi
