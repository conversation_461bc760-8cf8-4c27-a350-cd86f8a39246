import {
  IApiEndpointListResponse,
  IApiHistoryListResponse,
  IExportFileResponse,
  IGetTopLogParams
} from '@/types/top'
import service from '..'

const basePath = '/api-log'
const topLogApi = {
  getEndpointList: () => service.get<IApiEndpointListResponse>(`${basePath}/endpoints`),
  getTopLogList: (params: IGetTopLogParams) =>
    service.get<IApiHistoryListResponse>(basePath, params),
  exportAdminApiLog: (params: IGetTopLogParams) =>
    service.post<IExportFileResponse>(`${basePath}/export`, params)
}

export default topLogApi
